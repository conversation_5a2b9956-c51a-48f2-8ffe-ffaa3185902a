import nodemailer from "nodemailer";

// Common transporter utilities
/**
 * Create and configure SMTP transporter
 */
export const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.NODE_ENV === "production", // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

/**
 * Get the default from address configuration
 */
export const getFromAddress = () => ({
  name: "<PERSON>",
  address: process.env.SMTP_FROM || process.env.SMTP_USER || "",
});

/**
 * Get support email address
 */
export const getSupportEmail = () => {
  return process.env.SUPPORT_EMAIL || "<EMAIL>";
};

// Re-export all email functions for easy importing
export { sendConfirmationEmail } from "./confirmation";
export { sendNewsletterEmail } from "./newsletter";
export { sendSupportNotification, type NotificationData } from "./support";
export { sendEmailsToSubscribers, notifyIfHighFailureRate } from "./bulk";
