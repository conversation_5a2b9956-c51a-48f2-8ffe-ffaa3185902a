"use server";
import { client, clientWithToken } from "@/sanity/lib/client";
import { sendConfirmationEmail } from "../email/newsletter";
import {
  newsletterSchema,
  type NewsletterFormData,
} from "../schemas/newsletter";
import crypto from "crypto";
import { z } from "zod";
import { groq } from "next-sanity";

const handleResubscription = async (
  subscriberId: string,
  email: string,
  locale: string,
): Promise<SubscribeResult> => {
  const unsubscribeToken = crypto.randomUUID();

  const transaction = clientWithToken
    .transaction()
    .patch(subscriberId, (patch) =>
      patch
        .set({
          status: "active",
          subscribedAt: new Date().toISOString(),
          unsubscribeToken,
        })
        .unset(["unsubscribedAt"]),
    );

  try {
    // Construct unsubscribe URL with locale
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const unsubscribeUrl = `${baseUrl}/${locale}/unsubscribe?token=${unsubscribeToken}`;

    await sendConfirmationEmail(email, unsubscribeUrl);

    await transaction.commit();

    return { success: true };
  } catch (emailError) {
    console.error("Email sending failed for resubscription:", emailError);

    return {
      success: false,
      error:
        "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
    };
  }
};

const handleNewSubscription = async (
  email: string,
  locale: string,
): Promise<SubscribeResult> => {
  const unsubscribeToken = crypto.randomUUID();

  const transaction = clientWithToken.transaction().create({
    _type: "subscriber",
    email: email,
    status: "active",
    subscribedAt: new Date().toISOString(),
    unsubscribeToken,
  });

  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const unsubscribeUrl = `${baseUrl}/${locale}/unsubscribe?token=${unsubscribeToken}`;

    await sendConfirmationEmail(email, unsubscribeUrl);

    await transaction.commit();

    return { success: true };
  } catch (emailError) {
    console.error("Email sending failed for new subscription:", emailError);

    return {
      success: false,
      error:
        "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
    };
  }
};

export type SubscribeResult = {
  success: boolean;
  error?: string;
};
export async function subscribeToNewsletter({
  data,
  locale = "pt",
}: {
  data: NewsletterFormData;
  locale: string;
}): Promise<SubscribeResult> {
  try {
    const validatedData = newsletterSchema.parse(data);
    const findSubscriberQuery = groq`*[_type == "subscriber" && email == $email][0]`;

    const existingSubscriber = await client.fetch(findSubscriberQuery, {
      email: validatedData.email,
    });

    if (existingSubscriber) {
      if (existingSubscriber.status === "active") {
        return {
          success: false,
          error: "Este email já está inscrito na newsletter",
        };
      } else if (existingSubscriber.status === "unsubscribed") {
        return await handleResubscription(
          existingSubscriber._id,
          validatedData.email,
          locale,
        );
      }
    }

    return await handleNewSubscription(validatedData.email, locale);
  } catch (error) {
    console.error("Newsletter subscription error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Dados inválidos fornecidos",
      };
    }

    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}

export async function unsubscribeFromNewsletter({
  token,
}: {
  token: string;
}): Promise<SubscribeResult> {
  try {
    if (!token) {
      return {
        success: false,
        error: "Token de cancelamento inválido",
      };
    }
    const findSubscriberByUnsubscribeTokenQuery = groq`*[_type == "subscriber" && unsubscribeToken == $token][0]`;

    const subscriber = await client.fetch(
      findSubscriberByUnsubscribeTokenQuery,
      { token } as any,
    );

    if (!subscriber) {
      return {
        success: false,
        error: "Token de cancelamento inválido ou expirado",
      };
    }

    if (subscriber.status === "unsubscribed") {
      return {
        success: false,
        error: "Esta assinatura já foi cancelada",
      };
    }

    await clientWithToken
      .patch(subscriber._id)
      .set({
        status: "unsubscribed",
        unsubscribedAt: new Date().toISOString(),
      })
      .commit();

    return { success: true };
  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}
