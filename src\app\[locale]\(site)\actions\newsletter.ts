"use server";
import { client, clientWithToken } from "@/sanity/lib/client";
import {
  sendConfirmationEmail,
  sendNewsletterEmail,
} from "../email/newsletter";
import {
  newsletterSchema,
  type NewsletterFormData,
} from "../schemas/newsletter";
import crypto from "crypto";
import { z } from "zod";
import { groq } from "next-sanity";
import type { Newsletter, Subscriber } from "@/sanity/types";

const handleResubscription = async (
  subscriberId: string,
  email: string,
  locale: string,
): Promise<SubscribeResult> => {
  const unsubscribeToken = crypto.randomUUID();

  const transaction = clientWithToken
    .transaction()
    .patch(subscriberId, (patch) =>
      patch
        .set({
          status: "active",
          subscribedAt: new Date().toISOString(),
          unsubscribeToken,
        })
        .unset(["unsubscribedAt"]),
    );

  try {
    // Construct unsubscribe URL with locale
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const unsubscribeUrl = `${baseUrl}/${locale}/unsubscribe?token=${unsubscribeToken}`;

    await sendConfirmationEmail(email, unsubscribeUrl);

    await transaction.commit();

    return { success: true };
  } catch (emailError) {
    console.error("Email sending failed for resubscription:", emailError);

    return {
      success: false,
      error:
        "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
    };
  }
};

const handleNewSubscription = async (
  email: string,
  locale: string,
): Promise<SubscribeResult> => {
  const unsubscribeToken = crypto.randomUUID();

  const transaction = clientWithToken.transaction().create({
    _type: "subscriber",
    email: email,
    status: "active",
    subscribedAt: new Date().toISOString(),
    unsubscribeToken,
  });

  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const unsubscribeUrl = `${baseUrl}/${locale}/unsubscribe?token=${unsubscribeToken}`;

    await sendConfirmationEmail(email, unsubscribeUrl);

    await transaction.commit();

    return { success: true };
  } catch (emailError) {
    console.error("Email sending failed for new subscription:", emailError);

    return {
      success: false,
      error:
        "Falha ao enviar email de confirmação. Tente novamente mais tarde.",
    };
  }
};

export type SubscribeResult = {
  success: boolean;
  error?: string;
};
export async function subscribeToNewsletter({
  data,
  locale = "pt",
}: {
  data: NewsletterFormData;
  locale: string;
}): Promise<SubscribeResult> {
  try {
    const validatedData = newsletterSchema.parse(data);
    const findSubscriberQuery = groq`*[_type == "subscriber" && email == $email][0]`;

    const existingSubscriber = await client.fetch(findSubscriberQuery, {
      email: validatedData.email,
    });

    if (existingSubscriber) {
      if (existingSubscriber.status === "active") {
        return {
          success: false,
          error: "Este email já está inscrito na newsletter",
        };
      } else if (existingSubscriber.status === "unsubscribed") {
        return await handleResubscription(
          existingSubscriber._id,
          validatedData.email,
          locale,
        );
      }
    }

    return await handleNewSubscription(validatedData.email, locale);
  } catch (error) {
    console.error("Newsletter subscription error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Dados inválidos fornecidos",
      };
    }

    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}

export async function unsubscribeFromNewsletter({
  token,
}: {
  token: string;
}): Promise<SubscribeResult> {
  try {
    if (!token) {
      return {
        success: false,
        error: "Token de cancelamento inválido",
      };
    }
    const findSubscriberByUnsubscribeTokenQuery = groq`*[_type == "subscriber" && unsubscribeToken == $token][0]`;

    const subscriber = await client.fetch(
      findSubscriberByUnsubscribeTokenQuery,
      { token } as any,
    );

    if (!subscriber) {
      return {
        success: false,
        error: "Token de cancelamento inválido ou expirado",
      };
    }

    if (subscriber.status === "unsubscribed") {
      return {
        success: false,
        error: "Esta assinatura já foi cancelada",
      };
    }

    await clientWithToken
      .patch(subscriber._id)
      .set({
        status: "unsubscribed",
        unsubscribedAt: new Date().toISOString(),
      })
      .commit();

    return { success: true };
  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return {
      success: false,
      error: "Erro interno do servidor. Tente novamente mais tarde.",
    };
  }
}

export type SendNewslettersResult = {
  success: boolean;
  sentCount?: number;
  error?: string;
};

export async function sendNewsletters(
  newsletterId: string,
): Promise<SendNewslettersResult> {
  try {
    // Get the newsletter by ID
    const getNewsletterQuery = groq`*[_type == "newsletter" && _id == $newsletterId][0]`;
    const newsletter: Newsletter | null = await client.fetch(
      getNewsletterQuery,
      {
        newsletterId,
      },
    );

    if (!newsletter) {
      return {
        success: false,
        error: "Newsletter não encontrada",
      };
    }

    // Check if newsletter is already sent
    if (newsletter.status === "sent") {
      return {
        success: false,
        error: "Esta newsletter já foi enviada",
      };
    }

    // Get all active subscribers
    const getSubscribersQuery = groq`*[_type == "subscriber" && status == "active"]`;
    const subscribers: Subscriber[] = await client.fetch(getSubscribersQuery);

    if (subscribers.length === 0) {
      return {
        success: false,
        error: "Nenhum assinante ativo encontrado",
      };
    }

    console.log(
      `📧 Sending newsletter "${newsletter.subject}" to ${subscribers.length} subscribers...`,
    );

    // Send emails to all subscribers
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    let sentCount = 0;
    const failedEmails: string[] = [];

    for (const subscriber of subscribers) {
      try {
        const unsubscribeUrl = `${baseUrl}/pt/unsubscribe?token=${subscriber.unsubscribeToken}`;
        await sendNewsletterEmail(subscriber.email, newsletter, unsubscribeUrl);
        sentCount++;
        console.log(`✅ Newsletter sent to: ${subscriber.email}`);
      } catch (error) {
        console.error(
          `❌ Failed to send newsletter to ${subscriber.email}:`,
          error,
        );
        failedEmails.push(subscriber.email);
      }
    }

    // Update newsletter status to sent
    await clientWithToken
      .patch(newsletterId)
      .set({
        status: "sent",
        sentAt: new Date().toISOString(),
      })
      .commit();

    console.log(
      `📊 Newsletter sending completed: ${sentCount}/${subscribers.length} emails sent successfully`,
    );

    if (failedEmails.length > 0) {
      console.warn(
        `⚠️ Failed to send to ${failedEmails.length} emails:`,
        failedEmails,
      );
    }

    return {
      success: true,
      sentCount,
    };
  } catch (error) {
    console.error("Error sending newsletters:", error);
    return {
      success: false,
      error: "Erro interno do servidor ao enviar newsletters",
    };
  }
}
