import "../../globals.css";
import { cn } from "@/lib/utils";
import { mainFont } from "../../fonts";
import Header from "./containers/Header";
import { ReactLenis } from "@/lib/lenis";
import Footer from "./containers/Footer";

import { hasLocale, NextIntlClientProvider } from "next-intl";
import { getTranslations, setRequestLocale } from "next-intl/server";
import { Locale, routing } from "@/i18n/routing";
import { GoogleMapsEmbed } from "@next/third-parties/google";
import { notFound } from "next/navigation";
import { Toaster } from "sonner";

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const params = await props.params;

  const { locale } = params;

  const t = await getTranslations({
    locale: locale as Locale,
    namespace: "Metadata",
  });

  return {
    title: t("title"),
    description: t("description"),
  };
}

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function RootLayout({
  children,
  params,
}: Readonly<RootLayoutProps>) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  setRequestLocale(locale as Locale);

  return (
    <ReactLenis root>
      <html lang={locale} className="min-h-screen">
        <body
          className={cn(
            "from-primary to-primary-foreground flex min-h-screen flex-col bg-linear-to-b font-sans antialiased",
            mainFont.variable,
            // titleFont.variable,
          )}
        >
          <NextIntlClientProvider>
            <Header />
            {children}
            <Footer
              mapChildren={
                <GoogleMapsEmbed
                  apiKey={process.env.GOOGLE_MAPS_API_KEY || ""}
                  height={350}
                  width="100%"
                  mode="place"
                  q="259R+VG Luanda"
                  zoom="20"
                  language="pt"
                  maptype="satellite"
                  style="position: relative; width: 100%; height: 100%; opacity: 0; z-index: 10;"
                />
              }
            />
          </NextIntlClientProvider>
          <Toaster position="bottom-right" richColors />
        </body>
      </html>
    </ReactLenis>
  );
}
