import { defineType, defineField } from "sanity";

export const newsletterType = defineType({
  name: "newsletter",
  title: "Newsletter",
  type: "document",
  fields: [
    defineField({
      name: "subject",
      title: "Assunto",
      type: "string",
      description: "Linha de assunto do newsletter",
      validation: (Rule) =>
        Rule.required()
          .min(5)
          .max(150)
          .error("O assunto deve ter entre 5 e 150 caracteres"),
    }),
    defineField({
      name: "body",
      title: "Conteúdo",
      type: "blockContent",
      description: "Conteúdo principal do newsletter",
      validation: (Rule) => Rule.required().error("O conteúdo é obrigatório"),
    }),
    defineField({
      name: "status",
      title: "Estado",
      type: "string",
      description: "Estado atual do newsletter",
      options: {
        list: [
          { title: "Pendente", value: "pending" },
          { title: "Enviado", value: "sent" },
        ],
        layout: "radio",
      },
      initialValue: "pending",
      readOnly: true,
      validation: (Rule) => Rule.required().error("O status é obrigatório"),
    }),
  ],
  preview: {
    select: {
      title: "subject",
      status: "status",
    },
    prepare(selection) {
      const { title, status } = selection;
      const statusLabel = status === "pending" ? "Pendente" : "Enviado";
      return {
        title: title || "Newsletter sem assunto",
        subtitle: `Status: ${statusLabel}`,
      };
    },
  },
  orderings: [
    {
      title: "Mais recente primeiro",
      name: "createdDesc",
      by: [{ field: "_createdAt", direction: "desc" }],
    },
    {
      title: "Por status",
      name: "statusAsc",
      by: [{ field: "status", direction: "asc" }],
    },
  ],
});
