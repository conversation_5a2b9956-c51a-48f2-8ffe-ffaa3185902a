import nodemailer from "nodemailer";

/**
 * Create and configure SMTP transporter
 */
export const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.NODE_ENV === "production", // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

/**
 * Get the default from address configuration
 */
export const getFromAddress = () => ({
  name: "Pedro Yaba",
  address: process.env.SMTP_FROM || process.env.SMTP_USER || "",
});

/**
 * Get support email address
 */
export const getSupportEmail = () => {
  return process.env.SMTP_FROM || process.env.SMTP_USER || "";
};
