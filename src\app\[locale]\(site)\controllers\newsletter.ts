import { client, clientWithToken } from "@/sanity/lib/client";
import { sendNewsletterEmail } from "../email/newsletter";
import { groq } from "next-sanity";
import type { Newsletter, Subscriber } from "@/sanity/types";

export type SendNewslettersResult = {
  success: boolean;
  sentCount?: number;
  error?: string;
};

export async function getNewsletterById(
  newsletterId: string,
): Promise<Newsletter | null> {
  const getNewsletterQuery = groq`*[_type == "newsletter" && _id == $newsletterId][0]`;
  return await client.fetch(getNewsletterQuery, { newsletterId });
}

export async function getActiveSubscribers(): Promise<Subscriber[]> {
  const getSubscribersQuery = groq`*[_type == "subscriber" && status == "active"]`;
  return await client.fetch(getSubscribersQuery);
}

export async function sendEmailsToSubscribers(
  newsletter: Newsletter,
  subscribers: Subscriber[],
): Promise<{ sentCount: number; failedEmails: string[] }> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://pedroyaba.com";
  let sentCount = 0;
  const failedEmails: string[] = [];

  console.log(
    `📧 Sending newsletter "${newsletter.subject}" to ${subscribers.length} subscribers...`,
  );

  for (const subscriber of subscribers) {
    try {
      const unsubscribeUrl = `${baseUrl}/pt/unsubscribe?token=${subscriber.unsubscribeToken}`;
      await sendNewsletterEmail(subscriber.email, newsletter, unsubscribeUrl);
      sentCount++;
      console.log(`✅ Newsletter sent to: ${subscriber.email}`);
    } catch (error) {
      console.error(
        `❌ Failed to send newsletter to ${subscriber.email}:`,
        error,
      );
      failedEmails.push(subscriber.email);
    }
  }

  return { sentCount, failedEmails };
}

export async function updateNewsletterStatus(
  newsletterId: string,
): Promise<void> {
  await clientWithToken
    .patch(newsletterId)
    .set({
      status: "sent",
      sentAt: new Date().toISOString(),
    })
    .commit();
}

export async function sendNewsletters(
  newsletterId: string,
): Promise<SendNewslettersResult> {
  try {
    const newsletter = await getNewsletterById(newsletterId);

    if (!newsletter) {
      return {
        success: false,
        error: "Newsletter não encontrada",
      };
    }

    if (newsletter.status === "sent") {
      return {
        success: false,
        error: "Esta newsletter já foi enviada",
      };
    }

    const subscribers = await getActiveSubscribers();

    if (subscribers.length === 0) {
      return {
        success: false,
        error: "Nenhum assinante ativo encontrado",
      };
    }

    const { sentCount, failedEmails } = await sendEmailsToSubscribers(
      newsletter,
      subscribers,
    );

    await updateNewsletterStatus(newsletterId);

    console.log(
      `📊 Newsletter sending completed: ${sentCount}/${subscribers.length} emails sent successfully`,
    );

    if (failedEmails.length > 0) {
      console.warn(
        `⚠️ Failed to send to ${failedEmails.length} emails:`,
        failedEmails,
      );
    }

    return {
      success: true,
      sentCount,
    };
  } catch (error) {
    console.error("Error sending newsletters:", error);
    return {
      success: false,
      error: "Erro interno do servidor ao enviar newsletters",
    };
  }
}
