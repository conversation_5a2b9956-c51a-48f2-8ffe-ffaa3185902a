import { client, clientWithToken } from "@/sanity/lib/client";
import { sendNewsletterEmail } from "../email/newsletter";
import { groq } from "next-sanity";
import type { Newsletter, Subscriber } from "@/sanity/types";

export type SendNewslettersResult = {
  success: boolean;
  sentCount?: number;
  error?: string;
};

export async function sendNewsletters(
  newsletterId: string,
): Promise<SendNewslettersResult> {
  try {
    // Get the newsletter by ID
    const getNewsletterQuery = groq`*[_type == "newsletter" && _id == $newsletterId][0]`;
    const newsletter: Newsletter | null = await client.fetch(
      getNewsletterQuery,
      {
        newsletterId,
      },
    );

    if (!newsletter) {
      return {
        success: false,
        error: "Newsletter não encontrada",
      };
    }

    // Check if newsletter is already sent
    if (newsletter.status === "sent") {
      return {
        success: false,
        error: "Esta newsletter já foi enviada",
      };
    }

    // Get all active subscribers
    const getSubscribersQuery = groq`*[_type == "subscriber" && status == "active"]`;
    const subscribers: Subscriber[] = await client.fetch(getSubscribersQuery);

    if (subscribers.length === 0) {
      return {
        success: false,
        error: "Nenhum assinante ativo encontrado",
      };
    }

    console.log(
      `📧 Sending newsletter "${newsletter.subject}" to ${subscribers.length} subscribers...`,
    );

    // Send emails to all subscribers
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    let sentCount = 0;
    const failedEmails: string[] = [];

    for (const subscriber of subscribers) {
      try {
        const unsubscribeUrl = `${baseUrl}/pt/unsubscribe?token=${subscriber.unsubscribeToken}`;
        await sendNewsletterEmail(subscriber.email, newsletter, unsubscribeUrl);
        sentCount++;
        console.log(`✅ Newsletter sent to: ${subscriber.email}`);
      } catch (error) {
        console.error(
          `❌ Failed to send newsletter to ${subscriber.email}:`,
          error,
        );
        failedEmails.push(subscriber.email);
      }
    }

    // Update newsletter status to sent
    await clientWithToken
      .patch(newsletterId)
      .set({
        status: "sent",
        sentAt: new Date().toISOString(),
      })
      .commit();

    console.log(
      `📊 Newsletter sending completed: ${sentCount}/${subscribers.length} emails sent successfully`,
    );

    if (failedEmails.length > 0) {
      console.warn(
        `⚠️ Failed to send to ${failedEmails.length} emails:`,
        failedEmails,
      );
    }

    return {
      success: true,
      sentCount,
    };
  } catch (error) {
    console.error("Error sending newsletters:", error);
    return {
      success: false,
      error: "Erro interno do servidor ao enviar newsletters",
    };
  }
}
