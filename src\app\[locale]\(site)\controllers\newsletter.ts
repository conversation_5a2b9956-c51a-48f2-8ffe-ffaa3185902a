import { client, clientWithToken } from "@/sanity/lib/client";
import { sendNewsletterEmail } from "../email/newsletter";
import { groq } from "next-sanity";
import type { Newsletter, Subscriber } from "@/sanity/types";
import nodemailer from "nodemailer";

export type SendNewslettersResult = {
  success: boolean;
  sentCount?: number;
  error?: string;
};

type NotificationData = {
  totalSubscribers: number;
  sentCount: number;
  failedCount: number;
  failedEmails: string[];
  stoppedEarly: boolean;
};

async function notifySupport(
  newsletter: Newsletter,
  data: NotificationData,
): Promise<void> {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: process.env.NODE_ENV === "production",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    const supportEmail = process.env.SMTP_FROM || process.env.SMTP_USER || "";
    const failureRate = Math.round(
      (data.failedCount / data.totalSubscribers) * 100,
    );

    const subject = `🚨 Newsletter Sending ${data.stoppedEarly ? "STOPPED" : "COMPLETED"} - High Failure Rate (${failureRate}%)`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Newsletter Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .failed-emails { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .failed-emails ul { max-height: 200px; overflow-y: auto; }
        </style>
      </head>
      <body>
        <h2>🚨 Newsletter Alert - High Failure Rate</h2>

        <div class="alert">
          <strong>Status:</strong> ${data.stoppedEarly ? "EXECUTION STOPPED" : "COMPLETED WITH ISSUES"}<br>
          <strong>Reason:</strong> ${failureRate}% of emails failed (threshold: 10%)
        </div>

        <div class="stats">
          <h3>📊 Statistics</h3>
          <ul>
            <li><strong>Newsletter:</strong> ${newsletter.subject}</li>
            <li><strong>Total Subscribers:</strong> ${data.totalSubscribers}</li>
            <li><strong>Successfully Sent:</strong> ${data.sentCount}</li>
            <li><strong>Failed:</strong> ${data.failedCount}</li>
            <li><strong>Failure Rate:</strong> ${failureRate}%</li>
            <li><strong>Execution Status:</strong> ${data.stoppedEarly ? "Stopped Early" : "Completed"}</li>
          </ul>
        </div>

        <div class="failed-emails">
          <h3>❌ Failed Email Addresses</h3>
          <ul>
            ${data.failedEmails.map((email) => `<li>${email}</li>`).join("")}
          </ul>
        </div>

        <p><strong>Action Required:</strong> Please investigate the email delivery issues and consider re-sending to failed addresses after resolving the problem.</p>

        <p>Time: ${new Date().toISOString()}</p>
      </body>
      </html>
    `;

    await transporter.sendMail({
      from: {
        name: "Pedro Yaba Newsletter System",
        address: supportEmail,
      },
      to: supportEmail,
      subject,
      html: htmlContent,
      text: `
        Newsletter Alert - High Failure Rate

        Status: ${data.stoppedEarly ? "EXECUTION STOPPED" : "COMPLETED WITH ISSUES"}
        Newsletter: ${newsletter.subject}
        Total Subscribers: ${data.totalSubscribers}
        Successfully Sent: ${data.sentCount}
        Failed: ${data.failedCount}
        Failure Rate: ${failureRate}%

        Failed Emails:
        ${data.failedEmails.join("\n")}

        Time: ${new Date().toISOString()}
      `,
    });

    console.log(`📧 Support notification sent to: ${supportEmail}`);
  } catch (error) {
    console.error("❌ Failed to send support notification:", error);
  }
}

export async function getNewsletterById(
  newsletterId: string,
): Promise<Newsletter | null> {
  const getNewsletterQuery = groq`*[_type == "newsletter" && _id == $newsletterId][0]`;
  return await client.fetch(getNewsletterQuery, { newsletterId });
}

export async function getActiveSubscribers(): Promise<Subscriber[]> {
  const getSubscribersQuery = groq`*[_type == "subscriber" && status == "active"]`;
  return await client.fetch(getSubscribersQuery);
}

export async function sendEmailsToSubscribers(
  newsletter: Newsletter,
  subscribers: Subscriber[],
): Promise<{ sentCount: number; failedEmails: string[]; stopped?: boolean }> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://pedroyaba.com";
  let sentCount = 0;
  const failedEmails: string[] = [];
  const totalSubscribers = subscribers.length;
  const failureThreshold = Math.ceil(totalSubscribers * 0.1); // 10% threshold

  console.log(
    `📧 Sending newsletter "${newsletter.subject}" to ${totalSubscribers} subscribers...`,
  );
  console.log(`⚠️ Will stop if ${failureThreshold} or more emails fail`);

  for (const subscriber of subscribers) {
    try {
      const unsubscribeUrl = `${baseUrl}/pt/unsubscribe?token=${subscriber.unsubscribeToken}`;
      await sendNewsletterEmail(subscriber.email, newsletter, unsubscribeUrl);
      sentCount++;
      console.log(`✅ Newsletter sent to: ${subscriber.email}`);
    } catch (error) {
      console.error(
        `❌ Failed to send newsletter to ${subscriber.email}:`,
        error,
      );
      failedEmails.push(subscriber.email);

      // Check if we've reached the failure threshold
      if (failedEmails.length >= failureThreshold) {
        console.error(
          `🚨 CRITICAL: ${failedEmails.length}/${totalSubscribers} emails failed (${Math.round((failedEmails.length / totalSubscribers) * 100)}%). Stopping execution.`,
        );

        // Send notification to support
        await notifySupport(newsletter, {
          totalSubscribers,
          sentCount,
          failedCount: failedEmails.length,
          failedEmails,
          stoppedEarly: true,
        });

        return { sentCount, failedEmails, stopped: true };
      }
    }
  }

  return { sentCount, failedEmails, stopped: false };
}

export async function updateNewsletterStatus(
  newsletterId: string,
): Promise<void> {
  await clientWithToken
    .patch(newsletterId)
    .set({
      status: "sent",
      sentAt: new Date().toISOString(),
    })
    .commit();
}

export async function sendNewsletters(
  newsletterId: string,
): Promise<SendNewslettersResult> {
  try {
    const newsletter = await getNewsletterById(newsletterId);

    if (!newsletter) {
      return {
        success: false,
        error: "Newsletter não encontrada",
      };
    }

    if (newsletter.status === "sent") {
      return {
        success: false,
        error: "Esta newsletter já foi enviada",
      };
    }

    const subscribers = await getActiveSubscribers();

    if (subscribers.length === 0) {
      return {
        success: false,
        error: "Nenhum assinante ativo encontrado",
      };
    }

    const { sentCount, failedEmails, stopped } = await sendEmailsToSubscribers(
      newsletter,
      subscribers,
    );

    // Only update status if sending wasn't stopped due to high failure rate
    if (!stopped) {
      await updateNewsletterStatus(newsletterId);

      console.log(
        `📊 Newsletter sending completed: ${sentCount}/${subscribers.length} emails sent successfully`,
      );

      if (failedEmails.length > 0) {
        const failureRate = Math.round(
          (failedEmails.length / subscribers.length) * 100,
        );
        console.warn(
          `⚠️ Failed to send to ${failedEmails.length} emails (${failureRate}%):`,
          failedEmails,
        );

        // Notify support if failure rate is high but below stopping threshold (5-9%)
        if (failureRate >= 5) {
          await notifySupport(newsletter, {
            totalSubscribers: subscribers.length,
            sentCount,
            failedCount: failedEmails.length,
            failedEmails,
            stoppedEarly: false,
          });
        }
      }
    } else {
      console.error(
        `🚨 Newsletter sending STOPPED due to high failure rate: ${sentCount}/${subscribers.length} sent, ${failedEmails.length} failed`,
      );
    }

    return {
      success: !stopped,
      sentCount,
      error: stopped
        ? `Sending stopped due to high failure rate (${Math.round((failedEmails.length / subscribers.length) * 100)}%)`
        : undefined,
    };
  } catch (error) {
    console.error("Error sending newsletters:", error);
    return {
      success: false,
      error: "Erro interno do servidor ao enviar newsletters",
    };
  }
}
