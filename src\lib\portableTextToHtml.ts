import { toHTML } from '@portabletext/to-html';
import { urlFor } from '@/sanity/lib/image';
import type { BlockContent } from '@/sanity/types';

// Define the components for rendering different block types
const components = {
  types: {
    image: ({ value }: any) => {
      if (!value?.asset) return '';
      
      const imageUrl = urlFor(value.asset).width(600).height(400).url();
      const alt = value.alt || '';
      
      return `<img src="${imageUrl}" alt="${alt}" style="max-width: 100%; height: auto; margin: 20px 0; border-radius: 8px;" />`;
    },
  },
  marks: {
    link: ({ children, value }: any) => {
      const href = value?.href || '#';
      return `<a href="${href}" style="color: #2a4255; text-decoration: underline;">${children}</a>`;
    },
    strong: ({ children }: any) => `<strong>${children}</strong>`,
    em: ({ children }: any) => `<em>${children}</em>`,
  },
  block: {
    normal: ({ children }: any) => `<p style="margin: 16px 0; line-height: 1.6;">${children}</p>`,
    h1: ({ children }: any) => `<h1 style="font-size: 32px; font-weight: bold; margin: 24px 0 16px 0; color: #2a4255;">${children}</h1>`,
    h2: ({ children }: any) => `<h2 style="font-size: 28px; font-weight: bold; margin: 20px 0 12px 0; color: #2a4255;">${children}</h2>`,
    h3: ({ children }: any) => `<h3 style="font-size: 24px; font-weight: bold; margin: 18px 0 10px 0; color: #2a4255;">${children}</h3>`,
    h4: ({ children }: any) => `<h4 style="font-size: 20px; font-weight: bold; margin: 16px 0 8px 0; color: #2a4255;">${children}</h4>`,
    blockquote: ({ children }: any) => `<blockquote style="border-left: 4px solid #2a4255; padding-left: 16px; margin: 20px 0; font-style: italic; color: #666;">${children}</blockquote>`,
  },
  list: {
    bullet: ({ children }: any) => `<ul style="margin: 16px 0; padding-left: 24px;">${children}</ul>`,
  },
  listItem: {
    bullet: ({ children }: any) => `<li style="margin: 4px 0;">${children}</li>`,
  },
};

/**
 * Converts Sanity block content to HTML string
 * @param blockContent - The block content from Sanity
 * @returns HTML string
 */
export function blockContentToHtml(blockContent: BlockContent): string {
  if (!blockContent || !Array.isArray(blockContent)) {
    return '';
  }

  try {
    return toHTML(blockContent, { components });
  } catch (error) {
    console.error('Error converting block content to HTML:', error);
    return '<p>Error rendering content</p>';
  }
}

/**
 * Converts block content to plain text (for email text version)
 * @param blockContent - The block content from Sanity
 * @returns Plain text string
 */
export function blockContentToText(blockContent: BlockContent): string {
  if (!blockContent || !Array.isArray(blockContent)) {
    return '';
  }

  try {
    let text = '';
    
    blockContent.forEach((block) => {
      if (block._type === 'block' && block.children) {
        const blockText = block.children
          .map((child) => child.text || '')
          .join('');
        
        if (blockText.trim()) {
          // Add appropriate spacing based on block style
          switch (block.style) {
            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
              text += `\n\n${blockText.toUpperCase()}\n`;
              break;
            case 'blockquote':
              text += `\n\n"${blockText}"\n`;
              break;
            default:
              text += `\n${blockText}`;
          }
        }
      }
    });
    
    return text.trim();
  } catch (error) {
    console.error('Error converting block content to text:', error);
    return 'Error rendering content';
  }
}
